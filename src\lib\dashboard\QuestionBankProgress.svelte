<script lang="ts">
    import { <PERSON>, Axis, Spline, Svg, Highlight, Toolt<PERSON>, <PERSON><PERSON><PERSON>, Points } from 'layerchart';
    import { scaleBand, scaleLinear, scalePoint } from 'd3-scale';
    import { onSnapshot, doc } from 'firebase/firestore';
    import { browser } from '$app/environment';

    import H4 from '$lib/ui/typography/H4.svelte';
    import P1 from '$lib/ui/typography/P1.svelte';
    import { db } from '$lib/firebase/firestore';
    import { user } from '$lib/firebase/auth.svelte';
    import type { CompletedQuestion } from '$lib/types/question.types';

    // Loading and error states
    let isLoading = $state(true);
    let error = $state<string | null>(null);

    // Firebase data
    let completedQuestionsData = $state<CompletedQuestion | null>(null);

    // Process Firebase data into chart format
    interface DayData {
        day: string;
        questions: number;
        accuracy: number;
        date: string; // For tracking actual dates
    }

    // Helper function to get day abbreviation from date
    function getDayAbbreviation(date: Date): string {
        const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
        return days[date.getDay()];
    }

    // Helper function to format date as YYYY-MM-DD
    function formatDate(date: Date): string {
        return date.toISOString().split('T')[0];
    }

    // Helper function to safely create a date from timestamp
    function createSafeDate(timestamp: string): Date {
        const date = new Date(timestamp);
        // Check if the date is valid
        if (isNaN(date.getTime())) {
            console.warn('Invalid timestamp:', timestamp);
            return new Date(); // Return current date as fallback
        }
        return date;
    }

    // Helper function to get the last 7 days
    function getLast7Days(): Date[] {
        const days = [];
        const today = new Date();

        // Ensure we have a valid date
        if (isNaN(today.getTime())) {
            console.error('Invalid current date, using fallback');
            const fallbackDate = new Date('2024-01-01'); // Fallback date
            for (let i = 6; i >= 0; i--) {
                const date = new Date(fallbackDate);
                date.setDate(fallbackDate.getDate() - i);
                days.push(date);
            }
            return days;
        }

        for (let i = 6; i >= 0; i--) {
            const date = new Date(today);
            date.setDate(today.getDate() - i);
            days.push(date);
        }
        return days;
    }

    // Process completed questions data into last 7 days chart data
    function processQuestionData(data: CompletedQuestion | null): DayData[] {
        const last7Days = getLast7Days();

        if (!data?.data || !Array.isArray(data.data)) {
            console.warn('Invalid or missing question data:', data);
            return last7Days.map(date => ({
                day: getDayAbbreviation(date),
                questions: 0,
                accuracy: 0,
                date: formatDate(date)
            }));
        }

        // Initialize data structure for each of the last 7 days
        const dailyData: Record<string, { questions: number; correct: number; total: number }> = {};
        last7Days.forEach(date => {
            const dateStr = formatDate(date);
            dailyData[dateStr] = { questions: 0, correct: 0, total: 0 };
        });

        // Process each completed question
        data.data.forEach(item => {
            // Skip items with invalid or missing timestamps
            if (!item.timestamp) {
                console.warn('Question item missing timestamp:', item);
                return;
            }

            const questionDate = createSafeDate(item.timestamp);
            const dateStr = formatDate(questionDate);

            // Only count questions from the last 7 days
            if (dailyData[dateStr] !== undefined) {
                dailyData[dateStr].questions++;
                dailyData[dateStr].total++;
                if (item.wasAnswerCorrect) {
                    dailyData[dateStr].correct++;
                }
            }
        });

        // Convert to chart format with accuracy calculation
        return last7Days.map(date => {
            const dateStr = formatDate(date);
            const dayData = dailyData[dateStr];
            return {
                day: getDayAbbreviation(date),
                questions: dayData.questions,
                accuracy: dayData.total > 0
                    ? Math.round((dayData.correct / dayData.total) * 100)
                    : 0,
                date: dateStr
            };
        });
    }

    // Helper function to calculate overall accuracy for the last 7 days
    function calculateOverallAccuracy(data: DayData[]): number {
        const totalQuestions = data.reduce((sum, d) => sum + d.questions, 0);
        if (totalQuestions === 0) return 0;

        const weightedAccuracy = data.reduce((sum, d) => sum + (d.accuracy * d.questions), 0);
        return Math.round(weightedAccuracy / totalQuestions);
    }

    // Helper function to get previous 7 days data for comparison
    function getPrevious7DaysData(data: CompletedQuestion | null): { totalQuestions: number; avgAccuracy: number } {
        if (!data?.data) {
            return { totalQuestions: 0, avgAccuracy: 0 };
        }

        const today = new Date();
        const previous7DaysStart = new Date(today);
        previous7DaysStart.setDate(today.getDate() - 14); // 14 days ago
        const previous7DaysEnd = new Date(today);
        previous7DaysEnd.setDate(today.getDate() - 7); // 7 days ago

        let totalQuestions = 0;
        let correctAnswers = 0;

        data.data.forEach(item => {
            // Skip items with invalid or missing timestamps
            if (!item.timestamp) {
                return;
            }

            const questionDate = createSafeDate(item.timestamp);
            if (questionDate >= previous7DaysStart && questionDate < previous7DaysEnd) {
                totalQuestions++;
                if (item.wasAnswerCorrect) {
                    correctAnswers++;
                }
            }
        });

        const avgAccuracy = totalQuestions > 0 ? Math.round((correctAnswers / totalQuestions) * 100) : 0;
        return { totalQuestions, avgAccuracy };
    }

    // Helper function to calculate percentage change
    function calculatePercentageChange(current: number, previous: number): { value: number; isPositive: boolean; isZero: boolean } {
        if (previous === 0) {
            return { value: current > 0 ? 100 : 0, isPositive: current > 0, isZero: current === 0 };
        }
        const change = ((current - previous) / previous) * 100;
        return {
            value: Math.abs(Math.round(change)),
            isPositive: change > 0,
            isZero: Math.abs(change) < 1
        };
    }

    // Derived values for display
    let dsatData = $derived(processQuestionData(completedQuestionsData));
    let totalQuestions = $derived(dsatData.reduce((sum, d) => sum + d.questions, 0));
    let overallAccuracy = $derived(calculateOverallAccuracy(dsatData));
    let previous7DaysData = $derived(getPrevious7DaysData(completedQuestionsData));

    // Calculate percentage changes compared to previous 7 days
    let questionCountChange = $derived(calculatePercentageChange(totalQuestions, previous7DaysData.totalQuestions));
    let accuracyChange = $derived(calculatePercentageChange(overallAccuracy, previous7DaysData.avgAccuracy));

    // Firebase subscription
    let unsubscribe: (() => void) | null = null;

    $effect(() => {
        if (browser && $user?.uid) {
            isLoading = true;
            error = null;

            const completedQuestionsRef = doc(db, 'users', $user.uid, 'completedQuestions', 'dataDoc');

            unsubscribe = onSnapshot(
                completedQuestionsRef,
                (snapshot) => {
                    try {
                        if (snapshot.exists()) {
                            completedQuestionsData = snapshot.data() as CompletedQuestion;
                        } else {
                            completedQuestionsData = null;
                        }
                        isLoading = false;
                    } catch (err) {
                        console.error('Error processing question bank data:', err);
                        error = 'Failed to process progress data';
                        isLoading = false;
                    }
                },
                (err) => {
                    console.error('Error fetching question bank progress:', err);
                    error = 'Failed to load progress data';
                    isLoading = false;
                }
            );
        } else if (!$user) {
            // User not logged in
            completedQuestionsData = null;
            isLoading = false;
        }

        // Cleanup function
        return () => {
            if (unsubscribe) {
                unsubscribe();
                unsubscribe = null;
            }
        };
    });
</script>

<H4>Question Bank Progress</H4>

{#if isLoading}
    <div class="loading-container">
        <img src="/loading.gif" alt="Loading..." width="128" height="128">
    </div>
{:else if error}
    <div class="error-container">
        <P1>⚠️ {error}</P1>
        <P1>Please try refreshing the page.</P1>
    </div>
{:else}
    <div class="stat-container flex flex-col w-full">
        <div class="flex justify-between flex-row w-full" style="color: var(--sky-blue)">
            <div class="flex flex-row gap-2 items-center">
                <div class="legend-this-week"></div>
                <P1 isBold>Last 7 days</P1>
            </div>
            <div class="flex flex-row gap-2 items-center">
                <P1 isBold>{totalQuestions} questions</P1>
            </div>
        </div>
        <div class="flex justify-between flex-row w-full" style="color: var(--aquamarine)">
            <div class="flex flex-row gap-2 items-center">
                <div class="legend-accuracy"></div>
                <P1 isBold>Acc. rate</P1>
            </div>
            <div class="flex flex-row gap-2 items-center">
                <P1 isBold>{overallAccuracy}%</P1>
            </div>
        </div>
        <div class="flex justify-between flex-row w-full" style="color: #A7A7A7;">
            <div class="flex flex-row gap-2 items-center">
                <div class="legend-last-week"></div>
                <P1 isBold>Previous 7 days</P1>
            </div>
            <P1 isBold>{previous7DaysData.totalQuestions} questions</P1>
        </div>
    </div>
{/if}

{#if !isLoading && !error && $user}
    <div class="dsat-chart h-[300px] grid p-4">
        <div class="col-start-1 row-start-1 z-0">
            <BarChart
                data={dsatData}
                x="day"
                xScale={scaleBand().domain(dsatData.map(d => d.day)).padding(0.5)}
                series={[
                    { key: "questions", color: "var(--sky-blue)" }
                ]}
                props={{
                    xAxis: { format: "none" },
                    yAxis: { format: "metric" },
                    tooltip: { header: { format: "none" } },
                }}
                padding={{ left: 16, bottom: 24 }}
            />
        </div>

        <div class="col-start-1 row-start-1 z-10">
            <Chart
                data={dsatData}
                x="day"
                xScale={scalePoint().domain(dsatData.map(d => d.day)).padding(0.75)}
                y="accuracy"
                yDomain={[0, 100]}
                padding={{ left: 16, bottom: 24 }}
                tooltip={{ mode: "band" }}
                let:height
            >
                <Svg>
                    <Axis
                        placement="right"
                        scale={scaleLinear([0, 100], [height, 0])}
                        format={(v: number) => `${v}%`}
                    />
                    <Spline class="stroke-2" style="stroke: var(--aquamarine)" />
                    <Points class="fill-primary stroke-primary" />
                    <Highlight points lines />
                </Svg>

                <Tooltip.Root let:data classes={{ root: "bg-white rounded-md border border-black shadow-[0.125rem_0.125rem_0_#000000]" }}>
                    <Tooltip.Header>{data.day}</Tooltip.Header>
                    <Tooltip.List>
                        <Tooltip.Item label="Questions" value={data.questions} format="metric" />
                        <Tooltip.Item label="Accuracy" value={`${data.accuracy}%`} />
                        <Tooltip.Item label="Date" value={data.date} />
                    </Tooltip.List>
                </Tooltip.Root>
            </Chart>
        </div>
    </div>
{/if}

  


<style>
    * {
        --color-primary: 157, 79%, 63%; /* aquamarine in HSL */
        --color-secondary: 100, 50%, 50%;
    }
    
    .dsat-chart {
        width: 100%;
    }

    .legend-this-week {
        width: 8px;
        height: 16px;
        flex-shrink: 0;
        border: 1px solid #000;
        background: var(--sky-blue, #66E2FF);
    }

    .legend-accuracy {
        width: 10px;
        height: 10px;
        flex-shrink: 0;
        background: var(--aquamarine);
        border-radius: 5px;
    }

    .legend-last-week {
        width: 8px;
        height: 16px;
        flex-shrink: 0;
        border: 1px solid #000;
        background: #A7A7A7;
    }

    .loading-container,
    .error-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 2rem;
        text-align: center;
        min-height: 200px;
    }

    .error-container {
        color: var(--rose, #EB47AB);
    }
</style>
  


